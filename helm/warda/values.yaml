# Global configuration
global:
  # Image configuration
  image:
    registry: ""
    repository: warda
    tag: latest
    pullPolicy: Never

  # External access configuration for k3s NodePort
  externalAccess:
    # Your k3s VM IP
    nodeIP: "*************"
    # Local domain configuration
    domain: "warda.local"
    frontend:
      nodePort: 30090
      url: "http://frontend.local:30090"
      domain: "warda.local"
    backend:
      nodePort: 30091
      url: "http://backend.local:30091"
      domain: "api.warda.local"
    keycloak:
      nodePort: 30092
      url: "http://keycloak.local:30092"
      domain: "keycloak.local"

  postgresql:
    postgresqlUsername: postgres
    postgresqlPassword: postgres_password
    postgresqlDatabase: postgresql
    service:
      port: 5432
    persistence:
      enabled: true
      size: 8Gi

# PostgreSQL chart configuration
postgresql:
  enabled: true
  # Use values from global configuration
  postgresqlUsername: postgres
  postgresqlPassword: postgres_password
  postgresqlDatabase: postgresql
  service:
    port: 5432
  persistence:
    enabled: true
    size: 8Gi

# Keycloak configuration
keycloak:
  enabled: true

  # Image configuration
  image:
    repository: quay.io/keycloak/keycloak
    tag: "23.0.0"
    pullPolicy: IfNotPresent

  # Admin credentials
  admin:
    username: admin
    password: admin123

  # Database configuration
  database:
    host: warda-postgresql
    port: 5432
    name: keycloak
    username: keycloak
    password: keycloak_password

  # Hostname configuration - use local domain for k3s NodePort
  hostname: "auth.warda.local:30092"

  # Ingress configuration
  ingress:
    enabled: true
    className: ""
    annotations:
      nginx.ingress.kubernetes.io/proxy-buffer-size: "128k"
      nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    hosts:
      - host: keycloak.local
        paths:
          - path: /
            pathType: Prefix
    tls: []

  # Resource configuration
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi

  # Realm and client configuration
  realm:
    name: warda
    displayName: "Warda Platform"
    enabled: true
    frontendClient:
      clientId: warda-frontend
      name: "Warda Frontend"
      enabled: true
      publicClient: true
      directAccessGrantsEnabled: false
      standardFlowEnabled: true
      implicitFlowEnabled: false
      serviceAccountsEnabled: false
      redirectUris:
        - "http://warda.local:30090/*"
        - "http://*************:30090/*"
        - "http://frontend.local/*"
      webOrigins:
        - "http://warda.local:30090"
        - "http://*************:30090"
        - "http://frontend.local"
    backendClient:
      clientId: warda-backend
      name: "Warda Backend"
      enabled: true
      publicClient: false
      directAccessGrantsEnabled: true
      standardFlowEnabled: true
      serviceAccountsEnabled: true
      secret: backend-client-secret
      redirectUris:
        - "http://api.warda.local:30091/*"
        - "http://*************:30091/*"
        - "http://localhost:8080/*"
        - "http://backend.local/*"

frontend:
  enabled: true
  # Image configuration
  image:
    repository: warda/frontend
    tag: latest
    pullPolicy: Never
  # Mapbox configuration
  mapboxToken: "pk.eyJ1Ijoic2lldHNlbSIsImEiOiJjbWR1M2NmajIxNXNxMmtyMzIzenZwbW1mIn0.isXf_tnxfA4aBS3NeJxJkA"
  # Keycloak configuration
  keycloak:
    url: "http://keycloak.local"
    realm: "warda"
    clientId: "warda-frontend"

# Backend configuration
backend:
  enabled: true
  # Database URL for the backend
  databaseUrl: "***********************************************************/postgresql"

  image:
    repository: warda/backend
    tag: latest
    pullPolicy: Never
  env:
    RUST_LOG: "warda=debug,backend=debug,tower_http=debug"
  # Database configuration
  database:
    host: "warda-postgresql"
    port: 5432
    name: postgresql
    user: postgres
  # OAuth/Keycloak configuration
  oauth:
    keycloak:
      url: "http://warda-keycloak:8080"
      realm: "warda"
      clientId: "warda-backend"
      issuer: "http://warda-keycloak:8080/realms/warda"

# Migrate configuration
migrate:
  enabled: true
  # Use global image configuration by default
  image:
    repository: warda/migrate
    tag: latest
    pullPolicy: Never
  # Wait container configuration
  waitContainer:
    image: alpine
    tag: "3.18"